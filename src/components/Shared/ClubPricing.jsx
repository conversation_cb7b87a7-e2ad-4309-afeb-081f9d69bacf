import React, { useEffect, useState } from "react";
import RightSideModal from "Components/RightSideModal";
import MkdSDK from "Utils/MkdSDK";
import { showToast } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { tokenExpireError } from "Context/Auth";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { fCurrency } from "Utils/formatNumber";
import { locationOptions, generateTimeOptions } from "Utils/utils";
import DeleteModal from "Components/Modals/DeleteModal";
import LoadingSpinner from "Components/LoadingSpinner";
import { convertTo12Hour, convertTo24Hour } from "Utils/utils";
import Select from "react-select";

let sdk = new MkdSDK();

// Generate time options for the time selectors
const timeOptions = generateTimeOptions();

// Helper function to format time consistently
const formatTimeDisplay = (timeStr) => {
  if (!timeStr) return "";
  return timeStr.includes("AM") || timeStr.includes("PM")
    ? timeStr
    : convertTo12Hour(timeStr);
};

export default function ClubPricing({
  role,
  profileSettings,
  sports,
  fetchSettings,
  pricing,
  club,
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingPricing, setEditingPricing] = useState(null);
  const [showDeletePricingModal, setShowDeletePricingModal] = useState(false);
  const [deletingPricing, setDeletingPricing] = useState(false);
  const [selectedPricing, setSelectedPricing] = useState(null);
  const userRole = localStorage.getItem("role");

  const [formData, setFormData] = useState({
    sport_id: "",
    type: "",
    sub_type: "",
    is_general: false,
    is_sport_general: false, // New field to distinguish between global general price and sport-specific general price
    is_lesson: false, // New field for lesson pricing
    general_rate: "00.00",
    lesson_club_fixed_amount: "0.00", // Fixed amount for club from lessons
    lesson_club_percentage: "0", // Percentage of coach rate for club
    lesson_pricing_type: "fixed", // "fixed", "percentage", or "combination"
    price_by_hours: [
      {
        start_time: "",
        end_time: "",
        rate: "00.00",
      },
    ],
  });

  const [availableTypes, setAvailableTypes] = useState([]);
  const [availableSubtypes, setAvailableSubtypes] = useState([]);
  const [filteredEndTimeOptions, setFilteredEndTimeOptions] =
    useState(timeOptions);
  const [pricingConflict, setPricingConflict] = useState({
    hasConflict: false,
    message: "",
    conflictDetails: null,
  });

  // Update available types when sport changes
  useEffect(() => {
    if (formData.sport_id) {
      const selectedSport = sports?.find(
        (s) => s.id.toString() === formData.sport_id
      );
      if (selectedSport) {
        const types = selectedSport.sport_types
          .filter((t) => t.type) // Filter out empty types
          .map((t) => ({
            id: t.club_sport_type_id,
            label: t.type,
          }));
        setAvailableTypes(types);
        // Reset type and subtype when sport changes
        setFormData((prev) => ({
          ...prev,
          type: "",
          sub_type: "",
        }));
      }
    } else {
      setAvailableTypes([]);
    }
  }, [formData.sport_id, sports]);

  // Update available subtypes when type changes
  useEffect(() => {
    if (formData.sport_id && formData.type) {
      const selectedSport = sports?.find(
        (s) => s.id.toString() === formData.sport_id
      );
      if (selectedSport) {
        const selectedType = selectedSport.sport_types.find(
          (t) => t.type === formData.type
        );
        if (selectedType) {
          const subtypes = selectedType.subtype.map((st, index) => ({
            id: index + 1,
            name: st,
          }));
          setAvailableSubtypes(subtypes);
          // Reset subtype when type changes
          setFormData((prev) => ({
            ...prev,
            sub_type: "",
          }));
        }
      }
    } else {
      setAvailableSubtypes([]);
    }
  }, [formData.sport_id, formData.type, sports]);

  // Function to filter time options based on start time
  const getFilteredTimeOptions = (startTime) => {
    if (!startTime) return timeOptions;

    // Find the index of the selected start time
    const startTimeIndex = timeOptions.findIndex(
      (option) => option.value === startTime
    );

    if (startTimeIndex === -1) return timeOptions;

    // Return only options that come after the selected start time
    return timeOptions.filter((_, index) => index > startTimeIndex);
  };

  const handleInputChange = (e, index = null) => {
    let updatedFormData;

    if (index !== null) {
      const newPriceByHours = [...formData.price_by_hours];
      if (e.target.name === "start_time" || e.target.name === "end_time") {
        const time24 = e.target.value;
        newPriceByHours[index] = {
          ...newPriceByHours[index],
          [e.target.name]: time24,
        };

        // If start_time changed, update filtered end time options
        if (e.target.name === "start_time") {
          const filteredOptions = getFilteredTimeOptions(time24);
          setFilteredEndTimeOptions(filteredOptions);
        }
      } else {
        newPriceByHours[index] = {
          ...newPriceByHours[index],
          [e.target.name]: e.target.value,
        };
      }
      updatedFormData = {
        ...formData,
        price_by_hours: newPriceByHours,
      };
    } else {
      updatedFormData = {
        ...formData,
        [e.target.name]: e.target.value,
      };

      // If changing sport, type, or subtype, check for conflicts with the new selection
      if (["sport_id", "type", "sub_type"].includes(e.target.name)) {
        // First clear any existing conflict
        setPricingConflict({
          hasConflict: false,
          message: "",
          conflictDetails: null,
        });

        // Then check for conflicts after a short delay to allow state to update
        setTimeout(() => {
          checkTimeConflicts(updatedFormData);
        }, 100);
      }
    }

    setFormData(updatedFormData);

    // Check for conflicts if this is a time-related change
    if (
      index !== null &&
      (e.target.name === "start_time" || e.target.name === "end_time")
    ) {
      setTimeout(() => {
        checkTimeConflicts(updatedFormData);
      }, 100);
    }
  };

  // Check for time conflicts with existing pricing entries
  const checkTimeConflicts = (updatedFormData) => {
    // Only check if we have a valid sport/type/subtype selection and not in general pricing mode
    if (
      !updatedFormData.is_general &&
      updatedFormData.sport_id &&
      updatedFormData.type &&
      updatedFormData.sub_type
    ) {
      const sportId = parseInt(updatedFormData.sport_id);
      const type = updatedFormData.type;
      const subType = updatedFormData.sub_type;

      // Find existing pricing entries for this sport/type/subtype
      const existingPricingForSameCriteria = pricing?.filter(
        (p) =>
          (p.is_general === false || p.is_general === 0) &&
          p.sport_id === sportId &&
          p.type === type &&
          (p.subtype === subType || p.sub_type === subType) && // Check both field names
          (!editingPricing || p.id !== editingPricing.id)
      );

      // Check each time slot in the form against existing pricing entries
      for (const currentSlot of updatedFormData.price_by_hours) {
        // Skip if start or end time is not set
        if (!currentSlot.start_time || !currentSlot.end_time) {
          continue;
        }

        const currentStart = new Date(
          `2000/01/01 ${convertTo24Hour(currentSlot.start_time)}`
        );
        const currentEnd = new Date(
          `2000/01/01 ${convertTo24Hour(currentSlot.end_time)}`
        );

        // Skip invalid time ranges
        if (currentStart >= currentEnd) {
          continue;
        }

        // Check against existing pricing entries
        for (const existingPricing of existingPricingForSameCriteria || []) {
          if (existingPricing.price_by_hours) {
            const existingTimeSlots = JSON.parse(
              existingPricing.price_by_hours
            );

            for (const existingSlot of existingTimeSlots) {
              if (existingSlot.start_time && existingSlot.end_time) {
                const existingStart = new Date(
                  `2000/01/01 ${convertTo24Hour(existingSlot.start_time)}`
                );
                const existingEnd = new Date(
                  `2000/01/01 ${convertTo24Hour(existingSlot.end_time)}`
                );

                // Check for overlap
                if (currentStart < existingEnd && currentEnd > existingStart) {
                  // Format times for better error message
                  const currentStartFormatted = formatTimeDisplay(
                    currentSlot.start_time
                  );
                  const currentEndFormatted = formatTimeDisplay(
                    currentSlot.end_time
                  );
                  const existingStartFormatted = formatTimeDisplay(
                    existingSlot.start_time
                  );
                  const existingEndFormatted = formatTimeDisplay(
                    existingSlot.end_time
                  );

                  setPricingConflict({
                    hasConflict: true,
                    message: `Time slot (${currentStartFormatted} - ${currentEndFormatted}) overlaps with existing pricing (${existingStartFormatted} - ${existingEndFormatted}) at rate $${existingSlot.rate}/hour for the selected court.`,
                    conflictDetails: {
                      currentSlot: {
                        start: currentStartFormatted,
                        end: currentEndFormatted,
                        rate: currentSlot.rate,
                      },
                      existingSlot: {
                        start: existingStartFormatted,
                        end: existingEndFormatted,
                        rate: existingSlot.rate,
                      },
                    },
                  });
                  return true;
                }
              }
            }
          }
        }
      }
    }

    // No conflicts found
    setPricingConflict({
      hasConflict: false,
      message: "",
      conflictDetails: null,
    });
    return false;
  };

  // Handle select change for time inputs
  const handleTimeSelectChange = (option, name, index) => {
    const newPriceByHours = [...formData.price_by_hours];
    newPriceByHours[index] = {
      ...newPriceByHours[index],
      [name]: option ? option.label : "",
    };

    // If start_time changed, update filtered end time options
    if (name === "start_time" && option) {
      const filteredOptions = getFilteredTimeOptions(option.value);
      setFilteredEndTimeOptions(filteredOptions);
    }

    const updatedFormData = {
      ...formData,
      price_by_hours: newPriceByHours,
    };

    setFormData(updatedFormData);

    // Check for conflicts after a short delay to allow state to update
    setTimeout(() => {
      checkTimeConflicts(updatedFormData);
    }, 100);
  };

  const addPriceByHours = () => {
    setFormData({
      ...formData,
      price_by_hours: [
        ...formData.price_by_hours,
        {
          start_time: "",
          end_time: "",
          rate: "00.00",
        },
      ],
    });
  };

  const handleEditClick = (pricing) => {
    setEditingPricing(pricing);
    const parsedPriceByHours = pricing.price_by_hours
      ? JSON.parse(pricing.price_by_hours)
      : [
          {
            start_time: "",
            end_time: "",
            rate: "00.00",
          },
        ];

    // Format time values for display in the Select component
    const formattedPriceByHours = parsedPriceByHours.map((item) => ({
      ...item,
      start_time: item.start_time ? formatTimeDisplay(item.start_time) : "",
      end_time: item.end_time ? formatTimeDisplay(item.end_time) : "",
    }));

    // Check if this is a general pricing (has is_general flag set to true)
    const isGeneral = pricing.is_general === true || pricing.is_general === 1;

    // Check if this is lesson pricing
    const isLesson = pricing.is_lesson === true || pricing.is_lesson === 1;

    // Determine if this is a sport-specific general price
    // If it's general and has a sport_id, it's a sport-specific general price
    const isSportGeneral = isGeneral && pricing.sport_id;

    const updatedFormData = {
      sport_id: pricing.sport_id ? pricing.sport_id.toString() : "",
      type: pricing.type ? pricing.type.toString() : "",
      sub_type: pricing.subtype
        ? pricing.subtype.toString()
        : pricing.sub_type
        ? pricing.sub_type.toString()
        : "",
      is_general: isGeneral,
      is_sport_general: isSportGeneral,
      is_lesson: isLesson,
      general_rate: pricing.general_rate || "00.00",
      lesson_club_fixed_amount: pricing.lesson_club_fixed_amount || "0.00",
      lesson_club_percentage: pricing.lesson_club_percentage || "0",
      lesson_pricing_type: pricing.lesson_pricing_type || "fixed",
      price_by_hours: formattedPriceByHours,
    };

    setFormData(updatedFormData);
    setIsModalOpen(true);

    // Check for conflicts with the loaded pricing data
    setTimeout(() => {
      checkTimeConflicts(updatedFormData);
    }, 300);
  };

  const resetForm = () => {
    setFormData({
      sport_id: "",
      type: "",
      sub_type: "",
      is_general: false,
      is_sport_general: false,
      is_lesson: false,
      general_rate: "00.00",
      lesson_club_fixed_amount: "0.00",
      lesson_club_percentage: "0",
      lesson_pricing_type: "fixed",
      price_by_hours: [
        {
          start_time: "",
          end_time: "",
          rate: "00.00",
        },
      ],
    });
    setEditingPricing(null);
    setPricingConflict({
      hasConflict: false,
      message: "",
      conflictDetails: null,
    });
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    resetForm();
  };

  // Check if there's a conflict with existing pricing entries
  const checkForPricingConflicts = () => {
    // Check for lesson pricing conflict
    if (formData.is_lesson) {
      const sportId = parseInt(formData.sport_id);
      const type = formData.type || "";
      const subType = formData.sub_type || "";

      // Check if there's already lesson pricing for this exact combination
      const existingLessonPrice = pricing?.find(
        (p) =>
          (p.is_lesson === true || p.is_lesson === 1) &&
          p.sport_id === sportId &&
          p.type === type &&
          (p.subtype === subType || p.sub_type === subType) &&
          (!editingPricing || p.id !== editingPricing.id)
      );

      if (existingLessonPrice) {
        return {
          hasConflict: true,
          message:
            "Lesson pricing for this sport, type, and sub-type combination already exists. Please edit the existing lesson pricing instead.",
        };
      }

      // Check if there's already court pricing for this exact combination
      const existingCourtPrice = pricing?.find(
        (p) =>
          (p.is_lesson === false || p.is_lesson === 0 || !p.is_lesson) &&
          (p.is_general === false || p.is_general === 0) &&
          p.sport_id === sportId &&
          p.type === type &&
          (p.subtype === subType || p.sub_type === subType) &&
          (!editingPricing || p.id !== editingPricing.id)
      );

      if (existingCourtPrice) {
        return {
          hasConflict: true,
          message:
            "Court pricing for this sport, type, and sub-type combination already exists. You can have either court pricing or lesson pricing, but not both for the same combination.",
        };
      }
    }

    // Check for general price conflict
    if (formData.is_general) {
      if (!formData.is_sport_general) {
        // This is a global general price (for all sports)
        // Check if there's any global general pricing
        const existingGlobalGeneralPrice = pricing?.find(
          (p) =>
            (p.is_general === true || p.is_general === 1) &&
            (!p.sport_id || p.sport_id === 0 || p.sport_id === "0") && // Global general price has no sport_id
            // Skip the current pricing entry if we're editing
            (!editingPricing || p.id !== editingPricing.id)
        );

        if (existingGlobalGeneralPrice) {
          return {
            hasConflict: true,
            message:
              "A general price for all sports already exists. Only one general price for all sports is allowed.",
          };
        }
      } else {
        // This is a sport-specific general price
        // First, make sure a sport is selected
        if (!formData.sport_id) {
          return {
            hasConflict: true,
            message:
              "Please select a sport for the sport-specific general price.",
          };
        }

        // Check for existing sport-specific general pricing for this sport
        const sportId = parseInt(formData.sport_id);
        const existingSportGeneralPrice = pricing?.find(
          (p) =>
            (p.is_general === true || p.is_general === 1) &&
            p.sport_id === sportId &&
            // Skip the current pricing entry if we're editing
            (!editingPricing || p.id !== editingPricing.id)
        );

        if (existingSportGeneralPrice) {
          return {
            hasConflict: true,
            message:
              "A general price for this sport already exists. Please edit the existing general price instead of creating a new one.",
          };
        }
      }
    } else {
      // Check for specific price conflicts (sport/type/subtype)
      const sportId = parseInt(formData.sport_id);
      const type = formData.type || "";
      const subType = formData.sub_type || ""; // Using sub_type from form data

      // Find if there's already a specific price for this exact combination
      const existingSpecificPrice = pricing?.find(
        (p) =>
          (p.is_general === false || p.is_general === 0) &&
          (p.is_lesson === false || p.is_lesson === 0 || !p.is_lesson) &&
          p.sport_id === sportId &&
          p.type === type &&
          (p.subtype === subType || p.sub_type === subType) && // Check both field names
          (!editingPricing || p.id !== editingPricing.id)
      );

      if (existingSpecificPrice) {
        return {
          hasConflict: true,
          message:
            "A court price for this sport, type, and sub-type combination already exists. Please edit the existing price instead.",
        };
      }

      // Check if there's already lesson pricing for this exact combination
      const existingLessonPrice = pricing?.find(
        (p) =>
          (p.is_lesson === true || p.is_lesson === 1) &&
          p.sport_id === sportId &&
          p.type === type &&
          (p.subtype === subType || p.sub_type === subType) &&
          (!editingPricing || p.id !== editingPricing.id)
      );

      if (existingLessonPrice) {
        return {
          hasConflict: true,
          message:
            "Lesson pricing for this sport, type, and sub-type combination already exists. You can have either court pricing or lesson pricing, but not both for the same combination.",
        };
      }

      // Check for time slot overlaps
      if (formData.price_by_hours.length > 0) {
        // First, check for valid time ranges (end time must be after start time)
        const timeSlots = formData.price_by_hours.filter(
          (slot) => slot.start_time && slot.end_time
        );

        for (const slot of timeSlots) {
          const start = new Date(
            `2000/01/01 ${convertTo24Hour(slot.start_time)}`
          );
          const end = new Date(`2000/01/01 ${convertTo24Hour(slot.end_time)}`);

          if (start >= end) {
            return {
              hasConflict: true,
              message: `Invalid time range: ${formatTimeDisplay(
                slot.start_time
              )} - ${formatTimeDisplay(
                slot.end_time
              )}. End time must be after start time.`,
            };
          }
        }

        // Then check for overlaps within the current form data

        for (let i = 0; i < timeSlots.length; i++) {
          const slot1 = timeSlots[i];
          const start1 = new Date(
            `2000/01/01 ${convertTo24Hour(slot1.start_time)}`
          );
          const end1 = new Date(
            `2000/01/01 ${convertTo24Hour(slot1.end_time)}`
          );

          for (let j = i + 1; j < timeSlots.length; j++) {
            const slot2 = timeSlots[j];
            const start2 = new Date(
              `2000/01/01 ${convertTo24Hour(slot2.start_time)}`
            );
            const end2 = new Date(
              `2000/01/01 ${convertTo24Hour(slot2.end_time)}`
            );

            // Check for any overlap between time periods
            // Two time periods overlap if one starts before the other ends
            if (start1 < end2 && end1 > start2) {
              // Format times for better error message
              const slot1StartFormatted = formatTimeDisplay(slot1.start_time);
              const slot1EndFormatted = formatTimeDisplay(slot1.end_time);
              const slot2StartFormatted = formatTimeDisplay(slot2.start_time);
              const slot2EndFormatted = formatTimeDisplay(slot2.end_time);

              return {
                hasConflict: true,
                message: `Time slots cannot overlap within the same pricing entry. Your time slot (${slot1StartFormatted} - ${slot1EndFormatted}) at rate $${slot1.rate}/hour overlaps with another time slot (${slot2StartFormatted} - ${slot2EndFormatted}) at rate $${slot2.rate}/hour. Please adjust the time periods.`,
              };
            }
          }
        }

        // Now check for overlaps with existing pricing entries for the same sport/type/subtype
        // When editing, we need to check against other pricing entries, but not the one being edited
        const existingPricingForSameCriteria = pricing?.filter(
          (p) =>
            (p.is_general === false || p.is_general === 0) &&
            p.sport_id === sportId &&
            p.type === type &&
            (p.subtype === subType || p.sub_type === subType) && // Check both field names
            (!editingPricing || p.id !== editingPricing.id)
        );

        for (const existingPricing of existingPricingForSameCriteria || []) {
          if (existingPricing.price_by_hours) {
            const existingTimeSlots = JSON.parse(
              existingPricing.price_by_hours
            );

            for (const currentSlot of timeSlots) {
              const currentStart = new Date(
                `2000/01/01 ${convertTo24Hour(currentSlot.start_time)}`
              );
              const currentEnd = new Date(
                `2000/01/01 ${convertTo24Hour(currentSlot.end_time)}`
              );

              for (const existingSlot of existingTimeSlots) {
                if (existingSlot.start_time && existingSlot.end_time) {
                  const existingStart = new Date(
                    `2000/01/01 ${convertTo24Hour(existingSlot.start_time)}`
                  );
                  const existingEnd = new Date(
                    `2000/01/01 ${convertTo24Hour(existingSlot.end_time)}`
                  );

                  // Check for any overlap between time periods
                  // Two time periods overlap if one starts before the other ends
                  if (
                    currentStart < existingEnd &&
                    currentEnd > existingStart
                  ) {
                    // Format times for better error message
                    const currentStartFormatted = formatTimeDisplay(
                      currentSlot.start_time
                    );
                    const currentEndFormatted = formatTimeDisplay(
                      currentSlot.end_time
                    );
                    const existingStartFormatted = formatTimeDisplay(
                      existingSlot.start_time
                    );
                    const existingEndFormatted = formatTimeDisplay(
                      existingSlot.end_time
                    );

                    return {
                      hasConflict: true,
                      message: `Time slots cannot overlap with existing pricing periods for the same sport/type/subtype. Your time slot (${currentStartFormatted} - ${currentEndFormatted}) at rate $${currentSlot.rate}/hour overlaps with an existing time slot (${existingStartFormatted} - ${existingEndFormatted}) at rate $${existingSlot.rate}/hour. Please adjust the time periods or modify the existing pricing.`,
                    };
                  }
                }
              }
            }
          }
        }
      }
    }

    return { hasConflict: false };
  };

  const handleSubmit = async (e) => {
    if (e) {
      e.preventDefault();
    }

    // Validate lesson pricing fields
    if (formData.is_lesson) {
      if (!formData.sport_id || !formData.type || !formData.sub_type) {
        showToast(
          globalDispatch,
          "Please select sport, type, and sub-type for lesson pricing.",
          3000,
          "warning"
        );
        return;
      }

      if (
        formData.lesson_pricing_type === "fixed" ||
        formData.lesson_pricing_type === "combination"
      ) {
        if (
          !formData.lesson_club_fixed_amount ||
          parseFloat(formData.lesson_club_fixed_amount) < 0
        ) {
          showToast(
            globalDispatch,
            "Please enter a valid fixed amount for the club.",
            3000,
            "warning"
          );
          return;
        }
      }

      if (
        formData.lesson_pricing_type === "percentage" ||
        formData.lesson_pricing_type === "combination"
      ) {
        if (
          !formData.lesson_club_percentage ||
          parseFloat(formData.lesson_club_percentage) < 0 ||
          parseFloat(formData.lesson_club_percentage) > 100
        ) {
          showToast(
            globalDispatch,
            "Please enter a valid percentage (0-100) for the club.",
            3000,
            "warning"
          );
          return;
        }
      }
    }

    // Check for conflicts before submitting
    const { hasConflict, message } = checkForPricingConflicts();
    if (hasConflict) {
      showToast(globalDispatch, message, 3000, "warning");
      return;
    }

    // Also check for time conflicts with existing pricing
    if (pricingConflict.hasConflict) {
      showToast(globalDispatch, pricingConflict.message, 3000, "warning");
      return;
    }

    try {
      setIsSubmitting(true);

      // Convert time values from 12-hour format to 24-hour format for submission
      const formattedPriceByHours = formData.is_general
        ? [{ rate: formData.general_rate }]
        : formData.is_lesson
        ? [{ rate: "0" }] // Lessons don't use time-based pricing
        : formData.price_by_hours.map((item) => ({
            ...item,
            start_time: item.start_time ? convertTo24Hour(item.start_time) : "",
            end_time: item.end_time ? convertTo24Hour(item.end_time) : "",
          }));

      const formattedData = {
        price_by_hours: formattedPriceByHours,
        sport_id: formData.is_sport_general
          ? parseInt(formData.sport_id)
          : formData.is_general
          ? 0
          : parseInt(formData.sport_id),
        subtype: formData.sub_type, // Using 'subtype' as the field name in the API
        club_id: club?.id,
        type: formData.type,
        is_general: formData.is_general,
        is_lesson: formData.is_lesson,
        general_rate: formData.is_general ? formData.general_rate : null,
        lesson_club_fixed_amount: formData.is_lesson
          ? formData.lesson_club_fixed_amount
          : null,
        lesson_club_percentage: formData.is_lesson
          ? formData.lesson_club_percentage
          : null,
        lesson_pricing_type: formData.is_lesson
          ? formData.lesson_pricing_type
          : null,
      };

      if (editingPricing) {
        formattedData.club_pricing_id = editingPricing.id;
      }

      await sdk.callRawAPI(
        role === "admin"
          ? `/v3/api/custom/courtmatchup/admin/profile-edit/${profileSettings?.user?.id}`
          : `/v3/api/custom/courtmatchup/${userRole}/profile-edit`,
        {
          pricing: [formattedData],
        },
        "POST"
      );

      // Refresh the data after successful submission
      await fetchSettings();
      handleModalClose();
    } catch (error) {
      console.log(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeletePricing = async () => {
    setDeletingPricing(true);
    try {
      if (role === "admin") {
        sdk.setTable("club_pricing");
        await sdk.callRestAPI(
          {
            id: selectedPricing.id,
          },
          "DELETE"
        );
      } else {
        sdk.setTable("club_pricing");
        await sdk.callRestAPI(
          {
            id: selectedPricing.id,
          },
          "DELETE"
        );
      }
      showToast(
        globalDispatch,
        "Pricing deleted successfully",
        3000,
        "success"
      );
      fetchSettings();
      setShowDeletePricingModal(false);
    } catch (error) {
      console.log(error);
      setDeletingPricing(false);
    } finally {
      setDeletingPricing(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "club-ui",
      },
    });
  }, []);

  return (
    <div className="flex flex-col gap-4 p-5 ">
      {isLoading && <LoadingSpinner />}
      <div className="flex items-center justify-between">
        <div className="text-xl font-medium">Pricing</div>
        <button
          onClick={() => setIsModalOpen(true)}
          className="flex items-center gap-2 rounded-xl bg-primaryBlue px-4 py-2 text-white"
        >
          <span>Add new</span>
          <span className="text-xl">+</span>
        </button>
      </div>

      <div className="relative overflow-x-auto">
        <div className="w-full min-w-[1000px]">
          <table className="w-full border-separate border-spacing-y-2">
            <thead>
              <tr className="text-left text-sm text-gray-500">
                <th className="pb-4">Price</th>
                <th className="pb-4">Sport</th>
                <th className="pb-4">Type</th>
                <th className="pb-4">Sub-type</th>

                <th className="pb-4"></th>
              </tr>
            </thead>
            <tbody>
              {pricing?.length > 0 &&
                pricing?.map((row) => (
                  <tr key={row.id} className="overflow-hidden bg-white">
                    <td className="rounded-l-xl bg-white px-4 py-3 text-gray-600">
                      {row.is_lesson ? (
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center gap-1 font-medium text-blue-600">
                            <span>Lesson Pricing</span>
                            <div className="group relative">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                className="h-4 w-4 text-gray-500"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              <div className="absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100">
                                This defines the club's revenue share from
                                lessons for this sport/type/subtype combination.
                              </div>
                            </div>
                          </div>
                          <div className="text-sm text-gray-600">
                            {row.lesson_pricing_type === "fixed" && (
                              <span>
                                Fixed: {fCurrency(row.lesson_club_fixed_amount)}
                              </span>
                            )}
                            {row.lesson_pricing_type === "percentage" && (
                              <span>
                                Percentage: {row.lesson_club_percentage}% of
                                coach rate
                              </span>
                            )}
                            {row.lesson_pricing_type === "combination" && (
                              <span>
                                Fixed: {fCurrency(row.lesson_club_fixed_amount)}{" "}
                                + {row.lesson_club_percentage}% of coach rate
                              </span>
                            )}
                          </div>
                        </div>
                      ) : row.is_general ? (
                        <div className="flex items-center gap-1 font-medium">
                          <span>
                            {row.sport_id && row.sport_id !== 0
                              ? `Sport general price: ${fCurrency(
                                  row.general_rate
                                )}/hour`
                              : `Global general price: ${fCurrency(
                                  row.general_rate
                                )}/hour`}
                          </span>
                          <div className="group relative">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                              className="h-4 w-4 text-gray-500"
                            >
                              <path
                                fillRule="evenodd"
                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <div className="absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100">
                              {row.sport_id && row.sport_id !== 0
                                ? "This price will be applied to all types and sub-types of this sport for all times of day that are not otherwise defined."
                                : "This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."}
                            </div>
                          </div>
                        </div>
                      ) : (
                        row.price_by_hours &&
                        JSON.parse(row.price_by_hours).map((hour, index) => (
                          <div key={index}>
                            {hour.start_time && hour.end_time ? (
                              <>
                                {formatTimeDisplay(hour.start_time)} -{" "}
                                {formatTimeDisplay(hour.end_time)} :{" "}
                                {fCurrency(hour.rate)}
                              </>
                            ) : (
                              <>{fCurrency(hour.rate)}</>
                            )}
                          </div>
                        ))
                      )}
                    </td>
                    {/* <td className="bg-white px-4 py-3">{row.hours}</td> */}
                    <td className="bg-white px-4 py-3">
                      {sports.find((s) => s.id == row.sport_id)?.name || "--"}
                    </td>
                    <td className="bg-white px-4 py-3">
                      {locationOptions.find((s) => s.label == row.type)
                        ?.label || "--"}
                    </td>
                    <td className="bg-white px-4 py-3">
                      {row.subtype || "--"}
                    </td>
                    <td className="flex items-center gap-2 rounded-r-xl bg-white px-4 py-3">
                      <button
                        className="flex items-center justify-center"
                        onClick={() => handleEditClick(row)}
                      >
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M16.2422 5.25L18.75 7.75781M13.3652 18.121L18.7473 12.74C19.2378 12.2495 19.5125 11.5875 19.5125 10.8973C19.5125 10.2071 19.2378 9.54511 18.7473 9.05469L14.9566 5.26406C14.4662 4.77356 13.8042 4.49883 13.114 4.49883C12.4238 4.49883 11.7618 4.77356 11.2714 5.26406L5.88925 10.6462C5.64744 10.888 5.5 11.2091 5.5 11.5434V16.9255C5.5 17.6334 6.07284 18.2062 6.78075 18.2062H12.163C12.4972 18.2062 12.8184 18.0588 13.0602 17.817L13.3652 18.1221L13.3652 18.1221Z"
                            stroke="#868C98"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </button>
                      <button
                        onClick={() => {
                          setShowDeletePricingModal(true);
                          setSelectedPricing(row);
                        }}
                        className="flex items-center justify-center"
                      >
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3.68964 18.3144L2.94119 18.3627L3.68964 18.3144ZM16.3104 18.3144L17.0588 18.3627V18.3627L16.3104 18.3144ZM0.75 3C0.335786 3 0 3.33579 0 3.75C0 4.16421 0.335786 4.5 0.75 4.5V3ZM19.25 4.5C19.6642 4.5 20 4.16421 20 3.75C20 3.33579 19.6642 3 19.25 3V4.5ZM8.5 8.75C8.5 8.33579 8.16421 8 7.75 8C7.33579 8 7 8.33579 7 8.75H8.5ZM7 14.25C7 14.6642 7.33579 15 7.75 15C8.16421 15 8.5 14.6642 8.5 14.25H7ZM13 8.75C13 8.33579 12.6642 8 12.25 8C11.8358 8 11.5 8.33579 11.5 8.75H13ZM11.5 14.25C11.5 14.6642 11.8358 15 12.25 15C12.6642 15 13 14.6642 13 14.25H11.5ZM13.1477 3.93694C13.2509 4.33808 13.6598 4.57957 14.0609 4.47633C14.4621 4.37308 14.7036 3.9642 14.6003 3.56306L13.1477 3.93694ZM2.00156 3.79829L2.94119 18.3627L4.43808 18.2661L3.49844 3.70171L2.00156 3.79829ZM4.68756 20H15.3124V18.5H4.68756V20ZM17.0588 18.3627L17.9984 3.79829L16.5016 3.70171L15.5619 18.2661L17.0588 18.3627ZM17.25 3H2.75V4.5H17.25V3ZM0.75 4.5H2.75V3H0.75V4.5ZM17.25 4.5H19.25V3H17.25V4.5ZM15.3124 20C16.2352 20 16.9994 19.2835 17.0588 18.3627L15.5619 18.2661C15.5534 18.3976 15.4443 18.5 15.3124 18.5V20ZM2.94119 18.3627C3.0006 19.2835 3.76481 20 4.68756 20V18.5C4.55574 18.5 4.44657 18.3976 4.43808 18.2661L2.94119 18.3627ZM7 8.75V14.25H8.5V8.75H7ZM11.5 8.75V14.25H13V8.75H11.5ZM10 1.5C11.5134 1.5 12.7868 2.53504 13.1477 3.93694L14.6003 3.56306C14.0731 1.51451 12.2144 0 10 0V1.5ZM6.85237 3.93694C7.21319 2.53504 8.48668 1.5 10 1.5V0C7.78568 0 5.92697 1.51451 5.39971 3.56306L6.85237 3.93694Z"
                            fill="#868C98"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                ))}
              {pricing?.length === 0 && (
                <tr>
                  <td colSpan="5" className="text-center">
                    No pricing found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      <RightSideModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        title={editingPricing ? "Edit Pricing" : "Add Pricing"}
        onPrimaryAction={handleSubmit}
        submitting={isSubmitting}
        primaryButtonText={editingPricing ? "Update" : "Save"}
        primaryButtonDisabled={pricingConflict.hasConflict}
      >
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <label className="text-sm">Sport</label>
            <select
              name="sport_id"
              value={formData.sport_id}
              onChange={handleInputChange}
              className="rounded-lg border border-gray-200 px-3 py-2"
              disabled={formData.is_general && !formData.is_sport_general}
            >
              <option value="">- select -</option>
              {sports
                ?.filter((sport) => sport.status === 1)
                .map((sport) => (
                  <option key={sport.id} value={sport.id}>
                    {sport.name}
                  </option>
                ))}
            </select>
            {formData.is_general && !formData.is_sport_general && (
              <p className="mt-1 text-xs text-gray-500">
                Sport selection is disabled for global general pricing.
              </p>
            )}
            {formData.is_general &&
              formData.is_sport_general &&
              !formData.sport_id && (
                <p className="mt-1 text-xs text-red-500">
                  Please select a sport for sport-specific general pricing.
                </p>
              )}
            {formData.is_lesson && !formData.sport_id && (
              <p className="mt-1 text-xs text-red-500">
                Please select a sport for lesson pricing configuration.
              </p>
            )}
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm">Type</label>
            <select
              name="type"
              value={formData.type}
              onChange={handleInputChange}
              className="rounded-lg border border-gray-200 px-3 py-2"
              disabled={!formData.sport_id || formData.is_general}
            >
              <option value="">- select -</option>
              {availableTypes.map((type) => (
                <option key={type.id} value={type.label}>
                  {type.label}
                </option>
              ))}
            </select>
            {formData.is_general && (
              <p className="mt-1 text-xs text-gray-500">
                Type selection is disabled for general pricing.
              </p>
            )}
            {formData.is_lesson && !formData.type && formData.sport_id && (
              <p className="mt-1 text-xs text-red-500">
                Please select a type for lesson pricing configuration.
              </p>
            )}
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm">Sub-type</label>
            <select
              name="sub_type"
              value={formData.sub_type}
              onChange={handleInputChange}
              className="rounded-lg border border-gray-200 px-3 py-2"
              disabled={!formData.type || formData.is_general}
            >
              <option value="">- select -</option>
              {availableSubtypes.map((subtype) => (
                <option key={subtype.id} value={subtype.name}>
                  {subtype.name}
                </option>
              ))}
            </select>
            {formData.is_general && (
              <p className="mt-1 text-xs text-gray-500">
                Sub-type selection is disabled for general pricing.
              </p>
            )}
            {formData.is_lesson && !formData.sub_type && formData.type && (
              <p className="mt-1 text-xs text-red-500">
                Please select a sub-type for lesson pricing configuration.
              </p>
            )}
          </div>

          <div className="mt-2 flex flex-col gap-3">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="is_general"
                name="is_general"
                checked={formData.is_general}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    is_general: e.target.checked,
                    // If unchecking general, also uncheck sport-specific general
                    is_sport_general: e.target.checked
                      ? formData.is_sport_general
                      : false,
                  });
                }}
                className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
              />
              <div className="flex items-center gap-1">
                <label htmlFor="is_general" className="text-sm font-medium">
                  General price
                </label>
              </div>
            </div>

            {formData.is_general && (
              <div className="ml-6 flex flex-col gap-3">
                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    id="global_general"
                    name="general_type"
                    checked={!formData.is_sport_general}
                    onChange={() => {
                      setFormData({
                        ...formData,
                        is_sport_general: false,
                        sport_id: "", // Clear sport selection for global general price
                      });
                    }}
                    className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
                  />
                  <div className="flex items-center gap-1">
                    <label
                      htmlFor="global_general"
                      className="text-sm font-medium"
                    >
                      General price for all sports
                    </label>
                    <div className="group relative">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        className="h-4 w-4 text-gray-500"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <div className="absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100">
                        This price will be applied to all sports/types/sub-types
                        for all times of day that are not otherwise defined.
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    id="sport_general"
                    name="general_type"
                    checked={formData.is_sport_general}
                    onChange={() => {
                      setFormData({
                        ...formData,
                        is_sport_general: true,
                      });
                    }}
                    className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
                  />
                  <div className="flex items-center gap-1">
                    <label
                      htmlFor="sport_general"
                      className="text-sm font-medium"
                    >
                      General price for selected sport
                    </label>
                    <div className="group relative">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        className="h-4 w-4 text-gray-500"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <div className="absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100">
                        This price will be applied to all types and sub-types of
                        the selected sport for all times of day that are not
                        otherwise defined.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="mt-2 flex flex-col gap-3">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="is_lesson"
                name="is_lesson"
                checked={formData.is_lesson}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    is_lesson: e.target.checked,
                    // If unchecking lesson, reset lesson fields
                    lesson_club_fixed_amount: e.target.checked
                      ? formData.lesson_club_fixed_amount
                      : "0.00",
                    lesson_club_percentage: e.target.checked
                      ? formData.lesson_club_percentage
                      : "0",
                    lesson_pricing_type: e.target.checked
                      ? formData.lesson_pricing_type
                      : "fixed",
                  });
                }}
                className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
              />
              <div className="flex items-center gap-1">
                <label htmlFor="is_lesson" className="text-sm font-medium">
                  Lesson
                </label>
                <div className="group relative">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="h-4 w-4 text-gray-500"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div className="absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100">
                    Define the club's share of revenue from lessons for this
                    sport/type/subtype combination.
                  </div>
                </div>
              </div>
            </div>

            {formData.is_lesson && (
              <div className="ml-6 flex flex-col gap-3 rounded-lg border border-blue-200 bg-blue-50 p-4">
                <div className="text-sm font-medium text-blue-800">
                  Club Revenue Share Configuration
                </div>

                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="lesson_fixed"
                      name="lesson_pricing_type"
                      value="fixed"
                      checked={formData.lesson_pricing_type === "fixed"}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          lesson_pricing_type: e.target.value,
                        });
                      }}
                      className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
                    />
                    <label
                      htmlFor="lesson_fixed"
                      className="text-sm font-medium"
                    >
                      Fixed amount for club
                    </label>
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="lesson_percentage"
                      name="lesson_pricing_type"
                      value="percentage"
                      checked={formData.lesson_pricing_type === "percentage"}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          lesson_pricing_type: e.target.value,
                        });
                      }}
                      className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
                    />
                    <label
                      htmlFor="lesson_percentage"
                      className="text-sm font-medium"
                    >
                      Percentage of coach's rate
                    </label>
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="lesson_combination"
                      name="lesson_pricing_type"
                      value="combination"
                      checked={formData.lesson_pricing_type === "combination"}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          lesson_pricing_type: e.target.value,
                        });
                      }}
                      className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
                    />
                    <label
                      htmlFor="lesson_combination"
                      className="text-sm font-medium"
                    >
                      Combination of both
                    </label>
                  </div>
                </div>

                {(formData.lesson_pricing_type === "fixed" ||
                  formData.lesson_pricing_type === "combination") && (
                  <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium">
                      Fixed Amount for Club
                    </label>
                    <div className="flex rounded-lg border border-gray-200">
                      <span className="flex items-center border-r border-gray-200 px-3 text-sm text-gray-500">
                        USD
                      </span>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        name="lesson_club_fixed_amount"
                        value={formData.lesson_club_fixed_amount}
                        onChange={(e) => {
                          setFormData({
                            ...formData,
                            lesson_club_fixed_amount: e.target.value,
                          });
                        }}
                        className="flex-1 border-gray-200 px-3 py-2"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                )}

                {(formData.lesson_pricing_type === "percentage" ||
                  formData.lesson_pricing_type === "combination") && (
                  <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium">
                      Percentage of Coach Rate
                    </label>
                    <div className="flex rounded-lg border border-gray-200">
                      <input
                        type="number"
                        step="1"
                        min="0"
                        max="100"
                        name="lesson_club_percentage"
                        value={formData.lesson_club_percentage}
                        onChange={(e) => {
                          setFormData({
                            ...formData,
                            lesson_club_percentage: e.target.value,
                          });
                        }}
                        className="flex-1 border-gray-200 px-3 py-2"
                        placeholder="0"
                      />
                      <span className="flex items-center border-l border-gray-200 px-3 text-sm text-gray-500">
                        %
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {formData.is_general ? (
            <div className="flex flex-col gap-2 rounded-lg border border-gray-200 p-4">
              <label className="text-sm font-medium">
                General Price per Hour
              </label>
              <div className="flex rounded-lg border border-gray-200">
                <span className="flex items-center border-r border-gray-200 px-3 text-sm text-gray-500">
                  USD/h
                </span>
                <input
                  type="number"
                  name="general_rate"
                  value={formData.general_rate}
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      general_rate: e.target.value,
                    });
                  }}
                  className="flex-1 border-gray-200 px-3 py-2"
                />
              </div>
            </div>
          ) : formData.is_lesson ? (
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <div className="text-sm text-blue-800">
                <p className="font-medium">Lesson pricing configured</p>
                <p className="mt-1 text-xs">
                  Time-based pricing is not applicable for lessons. The club's
                  revenue share will be calculated based on the configuration
                  above.
                </p>
              </div>
            </div>
          ) : (
            <>
              {formData.price_by_hours.map((price, index) => (
                <div
                  key={index}
                  className="flex flex-col gap-4 rounded-lg border border-gray-200 p-4"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex flex-1 flex-col gap-2">
                      <div className="flex gap-4">
                        <div className="flex-1">
                          <label className="text-sm">From</label>
                          <Select
                            options={timeOptions}
                            className="w-full rounded-lg text-sm"
                            placeholder="Select start time"
                            value={
                              price.start_time
                                ? timeOptions.find(
                                    (option) =>
                                      option.label === price.start_time
                                  )
                                : null
                            }
                            onChange={(option) =>
                              handleTimeSelectChange(
                                option,
                                "start_time",
                                index
                              )
                            }
                          />
                        </div>
                        <div className="flex-1">
                          <label className="text-sm">Until</label>
                          <Select
                            options={filteredEndTimeOptions}
                            className="w-full rounded-lg text-sm"
                            placeholder={
                              price.start_time
                                ? "Select end time"
                                : "Select start time first"
                            }
                            value={
                              price.end_time
                                ? timeOptions.find(
                                    (option) => option.label === price.end_time
                                  )
                                : null
                            }
                            onChange={(option) =>
                              handleTimeSelectChange(option, "end_time", index)
                            }
                            isDisabled={!price.start_time}
                          />
                        </div>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        const newPrices = [...formData.price_by_hours];
                        newPrices.splice(index, 1);
                        setFormData({ ...formData, price_by_hours: newPrices });
                      }}
                      className="p-1 text-red-500 hover:text-red-700"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="h-5 w-5"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                        />
                      </svg>
                    </button>
                  </div>

                  <div className="flex flex-col gap-2">
                    <label className="text-sm">Price</label>
                    <div className="flex rounded-lg border border-gray-200">
                      <span className="flex items-center border-r border-gray-200 px-3 text-sm text-gray-500">
                        USD/h
                      </span>
                      <input
                        type="number"
                        name="rate"
                        value={price.rate}
                        onChange={(e) => handleInputChange(e, index)}
                        className="flex-1 border-gray-200 px-3 py-2"
                      />
                    </div>
                  </div>
                </div>
              ))}

              {pricingConflict.hasConflict && (
                <div className="mt-4 rounded-lg border border-red-200 bg-red-50 p-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-red-600"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Pricing Conflict Detected
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{pricingConflict.message}</p>
                        {pricingConflict.conflictDetails && (
                          <div className="mt-2 rounded border border-red-300 bg-red-100 p-2">
                            <p className="font-medium">Conflict Details:</p>
                            <p>
                              Your selection:{" "}
                              {
                                pricingConflict.conflictDetails.currentSlot
                                  .start
                              }{" "}
                              -{" "}
                              {pricingConflict.conflictDetails.currentSlot.end}{" "}
                              at $
                              {pricingConflict.conflictDetails.currentSlot.rate}
                              /hour
                            </p>
                            <p>
                              Existing pricing:{" "}
                              {
                                pricingConflict.conflictDetails.existingSlot
                                  .start
                              }{" "}
                              -{" "}
                              {pricingConflict.conflictDetails.existingSlot.end}{" "}
                              at $
                              {
                                pricingConflict.conflictDetails.existingSlot
                                  .rate
                              }
                              /hour
                            </p>
                          </div>
                        )}
                      </div>
                      <p className="mt-2 text-sm font-semibold text-red-800">
                        The Save button is disabled. Please adjust your time
                        selection to avoid overlapping with existing pricing.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <button
                type="button"
                onClick={addPriceByHours}
                className="mt-2 max-w-fit rounded-lg border border-gray-300 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50"
              >
                + Add Price Period
              </button>
            </>
          )}
        </form>
      </RightSideModal>
      <DeleteModal
        isOpen={showDeletePricingModal}
        onClose={() => setShowDeletePricingModal(false)}
        onConfirm={handleDeletePricing}
        title="Delete Pricing"
        message="Are you sure you want to delete this pricing?"
        loading={deletingPricing}
        onDelete={handleDeletePricing}
      />
    </div>
  );
}
