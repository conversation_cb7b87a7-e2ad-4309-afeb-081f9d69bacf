import React, { useState, useContext } from "react";
import { format } from "date-fns";
import BottomDrawer from "Components/Drawers/BottomDrawer";
import {
  formatTimeToSQL,
  calculateServiceFee,
  getTimeRange,
  reservationTypes,
  calculateCoachTotalFees,
  activityLogTypes,
  actionLogTypes,
} from "Utils/utils";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import { Link } from "react-router-dom";
import AddPlayers from "Components/Players/AddPlayers";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";
import { showToast, GlobalContext } from "Context/Global";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { useNavigate } from "react-router-dom";
import { BackButton } from "Components/BackButton";
import CheckoutForm from "Components/PaymentForm";
import { LessonReservationSummary } from "Components/Reservation/ReservationSummary";
import WarningModal from "Components/Modals/WarningModal";
import { useClub } from "Context/Club";
import Select from "react-select";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

export default function PlayersSelectionDrawer({
  isOpen,
  onClose,
  selectedDate,
  players,
  sports,
  selectedSport,
  groups,
  coach,
  selectedTimes,
  selectedType,
  selectedSubType,
  userProfile,
}) {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFindBuddyEnabled, setIsFindBuddyEnabled] = useState(false);

  const [playersNeeded, setPlayersNeeded] = useState(4);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [step, setStep] = useState(1);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [primaryPlayer, setPrimaryPlayer] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [defaultCard, setDefaultCard] = useState(null);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const [bookingId, setBookingId] = useState(null);
  const [reservationId, setReservationId] = useState(null);
  const [clientSecret, setClientSecret] = useState(null);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [paymentLoading, setPaymentLoading] = useState(false);

  // Add warning modal state
  const [warningModal, setWarningModal] = useState({
    isOpen: false,
    title: "",
    message: "",
    actionButtonText: "",
    actionButtonLink: "",
    type: "warning",
  });

  // Add club context
  const { user_subscription, user_permissions, club } = useClub();
  const user_id = localStorage.getItem("user");

  // Calculate total selected hours from all time slots
  const selectedHours = selectedTimes.reduce((total, timeSlot) => {
    const fromTime = new Date(`2000/01/01 ${timeSlot.from}`);
    const untilTime = new Date(`2000/01/01 ${timeSlot.until}`);
    const durationInHours = (untilTime - fromTime) / (1000 * 60 * 60);
    return total + durationInHours;
  }, 0);

  const fees = calculateCoachTotalFees({
    hourlyRate: coach?.hourly_rate,
    hours: selectedHours,
    playerCount: selectedPlayers.length,
    feeSettings: club?.fee_settings,
  });

  const handleIncrement = () => {
    setPlayersNeeded((prev) => Math.min(prev + 1, 10)); // Maximum 10 players
  };

  const handleDecrement = () => {
    setPlayersNeeded((prev) => Math.max(prev - 1, 0)); // Minimum 0 players
  };

  async function getCard() {
    try {
      setIsLoading(true);
      const {
        data: cards,
        limit,
        error,
        message,
      } = await sdk.getCustomerStripeCards();
      // console.log(cards);
      if (error) {
        showToast(globalDispatch, message, 5000, "error");
      }
      if (!cards) {
        return;
      }

      const defaultCard = cards?.data?.find(
        (card) => card.id === cards?.data[0]?.customer?.default_source
      );
      setDefaultCard(defaultCard);
    } catch (error) {
      console.error("ERROR", error);
      showToast(globalDispatch, error.message, 5000, "error");
      tokenExpireError(dispatch, error.code);
    } finally {
      setIsLoading(false);
    }
  }

  async function generatePaymentIntent() {
    try {
      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",
        { amount: fees.total },
        "POST"
      );

      setClientSecret(response.client_secret);
      setPaymentIntent(response.payment_intent);
    } catch (error) {
      console.error(error);
    } finally {
      setPaymentLoading(false);
    }
  }

  const handleNextPayment = async () => {
    // Check if user has a subscription
    if (!user_subscription?.planId) {
      setWarningModal({
        isOpen: true,
        title: "Subscription Required",
        message:
          "Please subscribe to a membership plan to book lessons with coaches",
        actionButtonText: "View Membership Plans",
        actionButtonLink: "/user/membership/buy",
        type: "warning",
      });
      return;
    }

    // Check if user's plan allows coach lessons
    if (!user_permissions?.allowCoach) {
      setWarningModal({
        isOpen: true,
        title: "Plan Upgrade Required",
        message: `Your current plan (${user_permissions?.planName}) does not include coach lessons. Please upgrade your plan.`,
        actionButtonText: "Upgrade Plan",
        actionButtonLink: "/user/membership/buy",
        type: "error",
      });
      return;
    }

    // Validate players selection
    if (!selectedPlayers.length) {
      setWarningModal({
        isOpen: true,
        title: "Players Required",
        message: "Please select at least one player",
        type: "warning",
      });
      return;
    }

    const { duration, start_time, end_time } = getTimeRange(selectedTimes);
    setIsSubmitting(true);

    try {
      const formattedDate = format(new Date(selectedDate), "yyyy-MM-dd");

      const payload = {
        sport_id: selectedSport,
        type: selectedType,
        sub_type: selectedSubType,
        date: formattedDate,
        start_time: start_time,
        end_time: end_time,
        duration,
        court_id: 1,
        price: fees.total,
        coach_fee: fees.coachFee,
        service_fee: fees.serviceFee,
        reservation_type: reservationTypes.lesson,
        player_ids: selectedPlayers.map((p) => p.id),
        primary_player_id: primaryPlayer?.id || userProfile?.id, // Add primary player ID
        buddy_details: null,
        payment_status: 0,
        payment_intent: null,
        coach_id: coach?.id,
      };

      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations",
        payload,
        "POST"
      );

      // Create activity log
      sdk.setTable("activity_logs");
      await sdk.callRestAPI(
        {
          user_id: user_id,
          activity_type: activityLogTypes.lesson,
          action_type: actionLogTypes.CREATE,
          data: JSON.stringify(payload),
          club_id: club?.id,
          description: "Created a lesson reservation",
        },
        "POST"
      );

      if (!response.error) {
        setReservationId(response.reservation_id);
        setBookingId(response.booking_id);
        await generatePaymentIntent();
        setStep(2);
      }
    } catch (error) {
      console.error("ERROR", error);
      setWarningModal({
        isOpen: true,
        title: "Reservation Error",
        message: error.message || "Error creating lesson reservation",
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchFamilyMembers = async () => {
    try {
      const familyResponse = await tdk.getList("user", {
        filter: [`guardian,eq,${user_id}`, `role,cs,user`],
      });
      setFamilyMembers(familyResponse.list);
    } catch (error) {
      console.error("Error fetching family members:", error);
    }
  };

  const handlePrimaryPlayerChange = (selectedOption) => {
    // Handle both Select component (with .value) and direct user object
    const newPrimaryPlayer = selectedOption.value || selectedOption;

    // Don't do anything if the same player is selected
    if (newPrimaryPlayer?.id === primaryPlayer?.id) {
      return;
    }

    setPrimaryPlayer(newPrimaryPlayer);

    // Update selected players to replace the current primary player with the new one
    setSelectedPlayers((prev) => {
      // Remove the current primary player if they're in the list
      const filteredPlayers = prev.filter((p) => p.id !== primaryPlayer?.id);

      // Check if the new primary player is already in the filtered list
      const isNewPlayerAlreadyInList = filteredPlayers.some(
        (p) => p.id === newPrimaryPlayer.id
      );

      if (isNewPlayerAlreadyInList) {
        // If new player is already in the list, just move them to the front
        const otherPlayers = filteredPlayers.filter(
          (p) => p.id !== newPrimaryPlayer.id
        );
        return [newPrimaryPlayer, ...otherPlayers];
      } else {
        // If new player is not in the list, add them at the beginning
        return [newPrimaryPlayer, ...filteredPlayers];
      }
    });
  };

  const togglePlayer = (player) => {
    setSelectedPlayers((prev) => {
      const isSelected = prev.some((p) => p.id === player.id);
      if (isSelected) {
        return prev.filter((p) => p.id !== player.id);
      }
      return [...prev, player];
    });
  };

  React.useEffect(() => {
    getCard({});
    fetchFamilyMembers();
  }, []);

  // Initialize primary player with current user
  React.useEffect(() => {
    if (userProfile && !primaryPlayer) {
      setPrimaryPlayer(userProfile);
    }
  }, [userProfile, primaryPlayer]);
  const lessonReservationDescription = club?.lesson_description
    ? JSON.parse(club?.lesson_description)
    : {
        reservation_description: "",
        payment_description: "",
      };
  return (
    <>
      <WarningModal
        isOpen={warningModal.isOpen}
        onClose={() => setWarningModal({ ...warningModal, isOpen: false })}
        title={warningModal.title}
        message={warningModal.message}
        actionButtonText={warningModal.actionButtonText}
        actionButtonLink={warningModal.actionButtonLink}
        type={warningModal.type}
      />
      <BottomDrawer
        isOpen={isOpen}
        onClose={onClose}
        title="Reservation detail"
      >
        <div className="mx-auto max-w-7xl space-y-6">
          <BackButton
            onBack={() => {
              if (step === 2) {
                setStep(1);
              } else {
                onClose();
              }
            }}
          />
          {step === 1 && (
            <div className="mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3">
              <LessonReservationSummary
                selectedSport={selectedSport}
                sports={sports}
                selectedType={selectedType}
                selectedSubType={selectedSubType}
                selectedDate={selectedDate}
                selectedTimes={selectedTimes}
                playersNeeded={playersNeeded}
                selectedCoach={coach}
                timeRange={getTimeRange(selectedTimes)}
              />

              <div className="space-y-4">
                <AddPlayers
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                  selectedPlayers={selectedPlayers}
                  setSelectedPlayers={setSelectedPlayers}
                  onPlayerToggle={togglePlayer}
                  players={players}
                  groups={groups}
                  selectedGroup={selectedGroup}
                  isFindBuddyEnabled={isFindBuddyEnabled}
                  setIsFindBuddyEnabled={setIsFindBuddyEnabled}
                  playersNeeded={playersNeeded}
                  handleIncrement={handleIncrement}
                  handleDecrement={handleDecrement}
                  showPlayersNeeded={false}
                  showAddReservationToFindBuddy={false}
                  familyMembers={familyMembers}
                  currentUser={primaryPlayer}
                  onCurrentUserChange={handlePrimaryPlayerChange}
                  userProfile={userProfile}
                />
              </div>

              <div className="h-fit rounded-lg bg-white shadow-5">
                <div className="rounded-lg bg-gray-50 p-4 text-center">
                  <h2 className="text-base font-medium">Reserving details</h2>
                </div>
                <div className="p-4">
                  <div className="space-y-2  ">
                    <div className="divide-y">
                      {/* Players count */}
                      <div className="py-3">
                        <p className="text-sm text-gray-500">
                          PLAYERS ({selectedPlayers.length})
                        </p>
                        <div className="mt-1">
                          {selectedPlayers.map((player) => (
                            <div key={player.id} className="text-sm">
                              {player.first_name} {player.last_name}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="py-3">
                        <p className="text-sm text-gray-500">COACH</p>
                        <div className="mt-1">
                          <div className="text-sm">
                            {coach?.user?.first_name} {coach?.user?.last_name}
                          </div>
                        </div>
                      </div>

                      {/* Fees section */}
                      <div className="py-3">
                        <p className="text-sm text-gray-500">FEES</p>

                        {/* Coach fee */}
                        <div className="mt-2 flex items-center justify-between">
                          <span className="flex items-center gap-1">
                            Coach fee
                            <span className="text-xs text-gray-500">
                              ({fCurrency(coach?.hourly_rate)}/hr ×{" "}
                              {selectedHours}hr × {selectedPlayers.length}{" "}
                              players)
                            </span>
                          </span>
                          <div className="flex items-center gap-2">
                            <span>{fCurrency(fees.coachFee)}</span>
                          </div>
                        </div>

                        {/* Service fee */}
                        <div className="flex items-center justify-between">
                          <span>Service fee</span>
                          <div className="flex items-center gap-2">
                            <span>{fCurrency(fees.serviceFee)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    {/* Total */}
                    <div className="flex items-center justify-between border-t pt-4">
                      <span>Total</span>
                      <span className="font-medium">
                        {fCurrency(fees.total)}
                      </span>
                    </div>

                    {/* Notification message */}
                    <div className="rounded-lg bg-[#F17B2C] p-3 text-sm text-white">
                      <div className="flex items-start gap-2">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z"
                            fill="white"
                          />
                        </svg>

                        <span>
                          After reserving, you will have 15 minutes to make the
                          payment.
                        </span>
                      </div>
                    </div>

                    {/* Make reservation button */}
                    <InteractiveButton
                      loading={isSubmitting}
                      onClick={handleNextPayment}
                      className="w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90"
                    >
                      <div className="flex flex-col items-center">
                        <span>Reserve Now</span>
                        <span className="text-sm opacity-80">
                          and continue to payment
                        </span>
                      </div>
                    </InteractiveButton>
                    <p className="text-center text-sm text-gray-500">
                      {lessonReservationDescription.reservation_description}
                    </p>
                    {/* Additional note */}
                    <p className="text-center text-sm text-gray-500">
                      (You will not be charged yet)
                    </p>

                    {/* Terms text */}
                    {/* <p className="text-sm text-gray-500">
                      By clicking "Make reservation" you agree to our{" "}
                      <Link
                        to="/terms-and-conditions"
                        target="_blank"
                        className="font-medium underline"
                      >
                        Terms and Conditions
                      </Link>{" "}
                      and{" "}
                      <Link
                        to="/privacy-policy"
                        target="_blank"
                        className="font-medium underline"
                      >
                        Privacy Policy
                      </Link>
                      . All sales are final unless stated otherwise. For any
                      issues, please contact our support team at{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="font-medium underline"
                      >
                        <EMAIL>
                      </a>
                      .
                    </p> */}
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div>
              <div className="mx-auto max-w-6xl">
                <div className="rounded-xl bg-[#F17B2C] px-4 py-3 text-white">
                  <div className="flex items-center gap-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <span>
                      Your session is reserved. You have 15 minutes to complete
                      the payment, otherwise the reservation will be canceled.
                    </span>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
                  <LessonReservationSummary
                    selectedSport={selectedSport}
                    sports={sports}
                    selectedType={selectedType}
                    selectedSubType={selectedSubType}
                    selectedDate={selectedDate}
                    selectedTimes={selectedTimes}
                    playersNeeded={playersNeeded}
                    timeRange={getTimeRange(selectedTimes)}
                    selectedCoach={coach}
                  />

                  <div className="space-y-6">
                    <div className="rounded-xl bg-white p-6 shadow-5">
                      <h2 className="mb-4 text-center text-lg font-medium">
                        Payment details
                      </h2>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="flex items-center gap-1">
                            Coach fee
                            <span className="text-xs text-gray-500">
                              ({fCurrency(coach?.hourly_rate)}/hr ×{" "}
                              {selectedHours}hr × {selectedPlayers.length}{" "}
                              players)
                            </span>
                          </span>
                          <span>{fCurrency(fees.coachFee)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-500">Service fee</span>
                          <span>{fCurrency(fees.serviceFee)}</span>
                        </div>
                        <div className="flex items-center justify-between border-t pt-4">
                          <span className="font-medium">Total</span>
                          <span className="font-medium">
                            {fCurrency(fees.total)}
                          </span>
                        </div>

                        <div>
                          <CheckoutForm
                            user={userProfile}
                            bookingId={bookingId}
                            reservationId={reservationId}
                            clientSecret={clientSecret}
                            paymentIntent={paymentIntent}
                            navigateRoute={`/user/payment-success/${reservationId}?type=lesson`}
                          />
                          <p className="text-sm text-gray-500">
                            {lessonReservationDescription.payment_description}
                          </p>
                        </div>

                        <p className="text-sm text-gray-500">
                          Lorem ipsum dolor sit amet, consectetur adipiscing
                          elit. Curabitur erat nisi, porta a ipsum eu, accumsan
                          dapibus enim. Donec ultrices congue libero in
                          convallis. Cras condimentum felis eget dignissim
                          tincidunt.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </BottomDrawer>
    </>
  );
}
